services:
  - type: web
    name: ocr-microservice
    env: docker
    dockerfilePath: ./Dockerfile
    envVars:
      # CORS Configuration
      - key: ALLOWED_ORIGINS
        value: https://contact-management-six-alpha.vercel.app,https://contact-management-ffsl.onrender.com
      
      # OpenAI Configuration (optional)
      - key: OPENAI_API_KEY
        sync: false
      
      # Service Configuration
      - key: PORT
        value: 8002
      
      # OCR Configuration
      - key: TESSERACT_PATH
        value: /usr/bin/tesseract
      - key: TESSDATA_PREFIX
        value: /usr/share/tesseract-ocr/4.00/tessdata/
      
      # Performance Settings
      - key: MAX_WORKERS
        value: 2
      - key: TIMEOUT_KEEP_ALIVE
        value: 65
    
    # Resource allocation
    plan: starter  # Can upgrade to standard for better performance
    
    # Health check
    healthCheckPath: /health
