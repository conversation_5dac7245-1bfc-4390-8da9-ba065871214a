# OCR Microservice Environment Configuration
# Copy this file to .env and fill in your values

# =============================================================================
# SERVICE CONFIGURATION
# =============================================================================
PORT=8002
ALLOWED_ORIGINS=https://contact-management-six-alpha.vercel.app,https://contact-management-ffsl.onrender.com

# =============================================================================
# LLM PROVIDER CONFIGURATION
# Choose at least one provider for intelligent contact extraction
# =============================================================================

# -----------------------------------------------------------------------------
# OpenAI (Recommended for production)
# Get API key from: https://platform.openai.com/api-keys
# -----------------------------------------------------------------------------
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_MODEL=gpt-3.5-turbo
# OPENAI_BASE_URL=  # Optional: for OpenAI-compatible APIs

# -----------------------------------------------------------------------------
# Groq (Recommended for development - has free tier)
# Get API key from: https://console.groq.com/keys
# -----------------------------------------------------------------------------
# GROQ_API_KEY=gsk_your-groq-api-key-here
# GROQ_MODEL=mixtral-8x7b-32768

# -----------------------------------------------------------------------------
# Anthropic Claude (High quality alternative)
# Get API key from: https://console.anthropic.com/
# -----------------------------------------------------------------------------
# ANTHROPIC_API_KEY=sk-ant-your-anthropic-key-here
# ANTHROPIC_MODEL=claude-3-haiku-20240307

# -----------------------------------------------------------------------------
# Google Gemini (Good balance of cost and quality)
# Get API key from: https://makersuite.google.com/app/apikey
# -----------------------------------------------------------------------------
# GEMINI_API_KEY=your-gemini-api-key-here
# GEMINI_MODEL=gemini-pro

# -----------------------------------------------------------------------------
# Together AI (Open source models)
# Get API key from: https://api.together.xyz/settings/api-keys
# -----------------------------------------------------------------------------
# TOGETHER_API_KEY=your-together-api-key-here
# TOGETHER_MODEL=meta-llama/Llama-2-7b-chat-hf

# -----------------------------------------------------------------------------
# Perplexity AI (Online models with web search)
# Get API key from: https://www.perplexity.ai/settings/api
# -----------------------------------------------------------------------------
# PERPLEXITY_API_KEY=pplx-your-perplexity-key-here
# PERPLEXITY_MODEL=llama-3.1-sonar-small-128k-online

# -----------------------------------------------------------------------------
# DeepSeek (Cost-effective Chinese provider)
# Get API key from: https://platform.deepseek.com/api_keys
# -----------------------------------------------------------------------------
# DEEPSEEK_API_KEY=sk-your-deepseek-key-here
# DEEPSEEK_MODEL=deepseek-chat

# =============================================================================
# OCR CONFIGURATION
# =============================================================================
TESSERACT_PATH=/usr/bin/tesseract
TESSDATA_PREFIX=/usr/share/tesseract-ocr/4.00/tessdata/

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================
MAX_WORKERS=2
TIMEOUT_KEEP_ALIVE=65

# =============================================================================
# PROVIDER RECOMMENDATIONS
# =============================================================================
# 
# For Development/Testing:
# - Use GROQ (free tier, fast)
# - Set: GROQ_API_KEY and GROQ_MODEL
#
# For Production (Best Quality):
# - Use OpenAI GPT-3.5-turbo or GPT-4
# - Set: OPENAI_API_KEY and OPENAI_MODEL
#
# For Production (Cost-Effective):
# - Use Anthropic Claude Haiku
# - Set: ANTHROPIC_API_KEY and ANTHROPIC_MODEL
#
# For High Volume:
# - Use multiple providers as fallbacks
# - Set multiple API keys
#
# =============================================================================
