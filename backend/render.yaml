services:
  - type: web
    name: contact-management-backend
    env: python
    buildCommand: "./build.sh"
    startCommand: "uvicorn api:app --host 0.0.0.0 --port $PORT --timeout-keep-alive 65 --timeout-graceful-shutdown 10"
    envVars:
      # Database Configuration
      - key: DATABASE_URL
        sync: false

      # CORS Configuration
      - key: ALLOWED_ORIGINS
        sync: false

      # Authentication Configuration (Set these in Render Dashboard)
      - key: JWT_SECRET_KEY
        sync: false
      - key: ADMIN_USERNAME
        sync: false
      - key: ADMIN_EMAIL
        sync: false
      - key: ADMIN_PASSWORD
        sync: false
      - key: ACCESS_TOKEN_EXPIRE_MINUTES
        value: 30

      # File Upload Configuration
      - key: MAX_FILE_SIZE
        value: 10485760
      - key: UPLOAD_DIR
        value: /tmp/uploads/

      # Timeout Configuration for Render (Very Aggressive)
      - key: OCR_TIMEOUT_SMALL
        value: 10
      - key: O<PERSON>_TIMEOUT_MEDIUM
        value: 15
      - key: OCR_TIMEOUT_LARGE
        value: 20
      - key: UPLOAD_TIMEOUT_OVERALL
        value: 25

      # File Size Limits for Render
      - key: MAX_IMAGE_SIZE_MB
        value: 1.5

      # OCR Configuration (using bundled tessdata)
      - key: TESSERACT_PATH
        value: /usr/bin/tesseract
      - key: TESSDATA_PREFIX
        value: ./tessdata

      # SpaCy Configuration
      - key: SPACY_MODEL
        value: en_core_web_sm

      # Production Settings
      - key: ENVIRONMENT
        value: production
      - key: DEBUG
        value: false
