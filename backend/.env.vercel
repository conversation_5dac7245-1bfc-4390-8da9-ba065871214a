# Vercel Production Environment Variables
# Copy these to your Vercel backend project's environment variables

# Database Configuration
# Neon PostgreSQL Database
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require

# Server Configuration
HOST=0.0.0.0
PORT=8000

# CORS Configuration - Update with your actual frontend URL
ALLOWED_ORIGINS=https://contact-management-six-alpha.vercel.app,http://localhost:5173

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_DIR=/tmp/uploads/

# OCR Configuration
TESSERACT_PATH=/usr/bin/tesseract

# Production Settings
ENVIRONMENT=production
DEBUG=false
