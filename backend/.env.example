# Database Configuration
# For testing with SQLite (no setup required)
DATABASE_URL=sqlite:///./contact_db.sqlite
# For PostgreSQL (Neon Database - replace with your connection string)
# DATABASE_URL=************************************************************

# Server Configuration
HOST=0.0.0.0
PORT=8000

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB in bytes
UPLOAD_DIR=uploads/

# OCR Configuration
TESSERACT_PATH=/usr/bin/tesseract
