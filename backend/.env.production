# Production Environment Variables for Render + Neon PostgreSQL

# Database Configuration (Neon PostgreSQL)
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require

# Server Configuration
HOST=0.0.0.0
PORT=8000

# CORS Configuration (Vercel frontend URL)
ALLOWED_ORIGINS=https://contact-management-six-alpha.vercel.app,http://localhost:5173,http://localhost:3000,http://localhost:5174

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_DIR=/tmp/uploads/

# SpaCy Model (Render will download this during build)
SPACY_MODEL=en_core_web_sm

# OCR Configuration for Render
TESSERACT_PATH=/usr/bin/tesseract

# Authentication Configuration (REQUIRED - Set in Render Environment Variables)
# JWT_SECRET_KEY=your-super-secret-jwt-key-minimum-32-characters-long
# ACCESS_TOKEN_EXPIRE_MINUTES=30

# Admin User Configuration (REQUIRED - Set in Render Environment Variables)
# ADMIN_USERNAME=admin
# ADMIN_EMAIL=<EMAIL>
# ADMIN_FULL_NAME=System Administrator
# ADMIN_PASSWORD=your-secure-admin-password-here

# Default User Configuration (OPTIONAL - Set in Render Environment Variables)
# DEFAULT_USER_USERNAME=user
# DEFAULT_USER_EMAIL=<EMAIL>
# DEFAULT_USER_FULL_NAME=Default User
# DEFAULT_USER_PASSWORD=your-secure-user-password-here

# Production Settings
ENVIRONMENT=production
DEBUG=false
